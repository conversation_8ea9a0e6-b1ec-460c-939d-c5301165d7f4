@extends('layouts.admin')

@section('title', 'My Profile - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">My Profile</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">My Profile</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Profile Information -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="bx bx-user-circle me-2"></i>Profile Information
                    </div>
                </div>
                <div class="card-body">
                    <div class="row gy-4">
                        <!-- Personal Information -->
                        <div class="col-xl-6">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Personal Information</div>
                                </div>
                                <div class="card-body">
                                    <div class="row gy-3">
                                        <div class="col-xl-12">
                                            <label class="form-label text-muted">Full Name</label>
                                            <div class="fw-semibold">{{ auth()->user()->name }}</div>
                                        </div>
                                        <div class="col-xl-12">
                                            <label class="form-label text-muted">Email Address</label>
                                            <div class="fw-semibold">{{ auth()->user()->email }}</div>
                                        </div>
                                        <div class="col-xl-12">
                                            <label class="form-label text-muted">Username</label>
                                            <div class="fw-semibold">{{ auth()->user()->username ?? 'Not set' }}</div>
                                        </div>
                                        <div class="col-xl-12">
                                            <label class="form-label text-muted">Account Type</label>
                                            <div>
                                                <span class="badge bg-success">
                                                    {{ ucfirst(str_replace('_', ' ', auth()->user()->role)) }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Status -->
                        <div class="col-xl-6">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Account Status</div>
                                </div>
                                <div class="card-body">
                                    <div class="row gy-3">
                                        <div class="col-xl-12">
                                            <label class="form-label text-muted">Email Verification</label>
                                            <div>
                                                @if (auth()->user()->email_verified_at)
                                                    <span class="badge bg-success">
                                                        <i class="ti ti-check me-1"></i>Verified
                                                    </span>
                                                @else
                                                    <span class="badge bg-danger">
                                                        <i class="ti ti-x me-1"></i>Not verified
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-xl-12">
                                            <label class="form-label text-muted">Account Status</label>
                                            <div>
                                                @if (auth()->user()->isLocked())
                                                    <span class="badge bg-danger">
                                                        <i class="ti ti-lock me-1"></i>Locked
                                                    </span>
                                                @else
                                                    <span class="badge bg-success">
                                                        <i class="ti ti-check me-1"></i>Active
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-xl-12">
                                            <label class="form-label text-muted">Member Since</label>
                                            <div class="fw-semibold">{{ auth()->user()->created_at->format('F d, Y') }}
                                            </div>
                                        </div>
                                        <div class="col-xl-12">
                                            <label class="form-label text-muted">Last Updated</label>
                                            <div class="fw-semibold">{{ auth()->user()->updated_at->format('F d, Y') }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Edit Profile Button -->
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="text-left mt-4">
                                <a href="{{ route('profile.edit') }}" class="btn btn-warning">
                                    <i class="ti ti-edit me-1"></i>Edit Profile Information
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
